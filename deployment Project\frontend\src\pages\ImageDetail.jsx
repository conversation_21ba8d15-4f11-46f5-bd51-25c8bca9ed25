import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { ArrowLeft, Download, Share2, Copy, Check, Eye, Calendar, Image as ImageIcon, Palette, Sparkles } from 'lucide-react';
import { getImageHistory } from '../utils/history';
import SEO from '../components/SEO';
import ShareButton from '../components/ShareButton';

const ImageDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [image, setImage] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    // Get image data from URL params or history
    const imageUrl = searchParams.get('url');
    const prompt = searchParams.get('prompt');
    const resolution = searchParams.get('resolution') || '1024x1024';
    
    if (imageUrl && prompt) {
      // Create image object from URL params
      setImage({
        id: Date.now(),
        imageUrl: decodeURIComponent(imageUrl),
        prompt: decodeURIComponent(prompt),
        resolution: resolution,
        createdAt: new Date().toISOString(),
        aspectRatio: resolution
      });
    } else if (id) {
      // Get from history by ID
      const history = getImageHistory();
      const foundImage = history.find(img => img.id === id);
      if (foundImage) {
        setImage(foundImage);
      } else {
        navigate('/history');
        return;
      }
    } else {
      navigate('/history');
      return;
    }
    
    setIsLoading(false);
  }, [id, searchParams, navigate]);

  const handleDownload = () => {
    if (!image?.imageUrl) return;

    // Navigate to download page using React Router
    const downloadUrl = `/image/download?url=${encodeURIComponent(image.imageUrl)}&prompt=${encodeURIComponent(image.prompt)}`;
    navigate(downloadUrl);
  };

  const handleCopyPrompt = async () => {
    if (!image?.prompt) return;
    
    try {
      await navigator.clipboard.writeText(image.prompt);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Copy failed:', error);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto"></div>
          <p className="mt-4 text-gray-300">Loading image details...</p>
        </div>
      </div>
    );
  }

  if (!image) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <ImageIcon className="w-16 h-16 text-gray-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-white mb-2">Image Not Found</h2>
          <p className="text-gray-400 mb-6">The requested image could not be found.</p>
          <button
            onClick={() => navigate('/history')}
            className="px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
          >
            Back to History
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      <SEO
        title={`AI Generated Image - ${image.prompt.substring(0, 50)}...`}
        description={`View and download this AI generated image: ${image.prompt}`}
        keywords="AI image, generated art, download, share"
      />

      {/* Header */}
      <div className="sticky top-0 z-50 bg-gray-900/95 backdrop-blur-sm border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <button
              onClick={() => navigate('/history')}
              className="flex items-center gap-2 text-gray-300 hover:text-white transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
              <span className="font-medium">Back to Gallery</span>
            </button>
            
            <div className="flex items-center gap-3">
              <ShareButton
                imageUrl={image.imageUrl}
                prompt={image.prompt}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                <Share2 className="w-4 h-4" />
                <span className="hidden sm:inline">Share</span>
              </ShareButton>
              
              <button
                onClick={handleDownload}
                className="flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
              >
                <Download className="w-4 h-4" />
                <span className="hidden sm:inline">Download</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid lg:grid-cols-2 gap-8 items-start">
          {/* Image */}
          <div className="space-y-4">
            <div className="relative group">
              <img
                src={image.imageUrl}
                alt={image.prompt}
                className="w-full rounded-2xl shadow-2xl"
                onError={(e) => {
                  e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzk5YTNhZiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vdCBhdmFpbGFibGU8L3RleHQ+PC9zdmc+';
                }}
              />
              
              {/* Full Screen Button */}
              <button
                onClick={() => window.open(image.imageUrl, '_blank')}
                className="absolute top-4 right-4 p-2 bg-gray-900/80 backdrop-blur-sm hover:bg-gray-800 rounded-lg transition-colors opacity-0 group-hover:opacity-100"
              >
                <Eye className="w-5 h-5 text-white" />
              </button>
            </div>
          </div>

          {/* Details */}
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold text-white mb-4 flex items-center gap-3">
                <Sparkles className="w-8 h-8 text-purple-500" />
                AI Generated Image
              </h1>
            </div>

            {/* Prompt */}
            <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-lg font-semibold text-white flex items-center gap-2">
                  <Palette className="w-5 h-5 text-purple-500" />
                  Prompt
                </h3>
                <button
                  onClick={handleCopyPrompt}
                  className="flex items-center gap-2 px-3 py-1 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded-lg transition-colors text-sm"
                >
                  {copied ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                  {copied ? 'Copied!' : 'Copy'}
                </button>
              </div>
              <p className="text-gray-300 leading-relaxed">{image.prompt}</p>
            </div>

            {/* Image Info */}
            <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                <ImageIcon className="w-5 h-5 text-blue-500" />
                Image Details
              </h3>
              
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Resolution:</span>
                  <span className="text-white font-medium">{image.resolution || image.aspectRatio || '1024x1024'}</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Created:</span>
                  <span className="text-white font-medium">{formatDate(image.createdAt)}</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Type:</span>
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-purple-900 to-pink-900 text-purple-300">
                    AI Generated
                  </span>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-4">
              <button
                onClick={handleDownload}
                className="flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-medium rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                <Download className="w-5 h-5" />
                Download Image
              </button>
              
              <ShareButton
                imageUrl={image.imageUrl}
                prompt={image.prompt}
                className="flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                <Share2 className="w-5 h-5" />
                Share Image
              </ShareButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ImageDetail;
