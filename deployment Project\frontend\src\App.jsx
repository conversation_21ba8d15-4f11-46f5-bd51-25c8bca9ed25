import { BrowserRouter as Router, Routes, Route, useNavigate } from 'react-router-dom';
import StyledSocialIcons from './components/StyledSocialIcons';
import Navigation from './components/Navigation';
import Footer from './components/Footer';
import ScrollToTop from './components/ScrollToTop';
import Home from './pages/Home';
import Generate from './pages/Generate';
import History from './pages/History';
import ImageDetail from './pages/ImageDetail';
import About from './pages/About';
import FAQ from './pages/FAQ';
import Terms from './pages/Terms';
import Blog from './pages/Blog';
import AdminPanel from './pages/AdminPanel';
import ImageDownload from './pages/ImageDownload';
import NotFound from './pages/NotFound';

import { useLocation } from 'react-router-dom';
import ReactGA from "react-ga4";
import { useEffect } from 'react';

const AppContent = () => {

  const location = useLocation();
  ReactGA.initialize("G-6RJ0LDGRT3");
useEffect(() => {
  ReactGA.send({ hitType: "pageview", page: location.pathname + location.search, title:"Gen Free AI" });

  
}, [location]);




  const navigate = useNavigate();

  const handleTermsClick = () => {
    navigate('/terms');
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Scroll to top on route change */}
      <ScrollToTop />

      {/* Navigation */}
      <Navigation />

      {/* Main content */}
      <main className="relative">
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/generate" element={<Generate />} />
          <Route path="/history" element={<History />} />
          <Route path="/image-detail" element={<ImageDetail />} />
          <Route path="/image-detail/:id" element={<ImageDetail />} />
          <Route path="/about" element={<About />} />
          <Route path="/faq" element={<FAQ />} />
          <Route path="/terms" element={<Terms />} />
          {/* <Route path="/blog" element={<Blog />} />
          <Route path="/blog/:slug" element={<Blog />} /> */}
          <Route path="/image/download" element={<ImageDownload />} />
          <Route path='/admin/pass="4.67Ssc5.00"' element ={<AdminPanel/>}/>

          <Route path="*" element={<NotFound />} />
        </Routes>
      </main>

      {/* Footer */}
      <Footer />


    </div>
  );
};

function App() {
  return (
    <Router>
      <AppContent />
    </Router>
  );
}

export default App;