import { Link, useLocation } from 'react-router-dom';
import { ChevronRight } from 'lucide-react';

const Breadcrumbs = ({ customBreadcrumbs = null, className = "" }) => {
  const location = useLocation();
  
  // If custom breadcrumbs are provided, use them
  if (customBreadcrumbs) {
    return (
      <nav className={`flex items-center space-x-2 text-sm text-gray-400 dark:text-gray-400 mb-6 ${className}`} aria-label="Breadcrumb">
        <ol className="flex items-center space-x-2">
          {customBreadcrumbs.map((crumb, index) => (
            <li key={index} className="flex items-center">
              {index > 0 && <ChevronRight className="w-4 h-4 mx-2 text-gray-400" />}
              {crumb.href ? (
                <Link 
                  to={crumb.href} 
                  className="hover:text-blue-400 dark:hover:text-blue-400 transition-colors duration-200 flex items-center"
                >
                  {crumb.icon && <crumb.icon className="w-4 h-4 mr-1" />}
                  {crumb.label}
                </Link>
              ) : (
                <span className="text-white  dark:text-white font-medium flex items-center">
                  {crumb.icon && <crumb.icon className="w-4 h-4 mr-1" />}
                  {crumb.label}
                </span>
              )}
            </li>
          ))}
        </ol>
      </nav>
    );
  }

  // Auto-generate breadcrumbs based on current location
  const pathnames = location.pathname.split('/').filter((x) => x);
  
  // Don't show breadcrumbs on homepage
  if (pathnames.length === 0) {
    return null;
  }

  // Define page titles and icons for better breadcrumb labels
  const pageLabels = {
    'generate': { label: 'Generator', icon: null },
    'blog': { label: 'Blog', icon: null },
    'about': { label: 'About Us', icon: null },
    'faq': { label: 'FAQ', icon: null },
    'terms': { label: 'Terms of Service', icon: null },
    'privacy': { label: 'Privacy Policy', icon: null },
    'history': { label: 'Generation History', icon: null },
    'download': { label: 'Download Image', icon: null },
    'contact': { label: 'Contact', icon: null },
    'help': { label: 'Help', icon: null },
    'guides': { label: 'Guides', icon: null },
    'tutorials': { label: 'Tutorials', icon: null },
    'gallery': { label: 'Gallery', icon: null },
    'pricing': { label: 'Pricing', icon: null },
    'features': { label: 'Features', icon: null }
  };

  // Function to get readable label for path segment
  const getLabel = (segment) => {
    if (pageLabels[segment]) {
      return pageLabels[segment].label;
    }
    
    // Convert kebab-case to title case
    return segment
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Build breadcrumb items
  const breadcrumbItems = [];

  // Special handling for /generate route - don't show breadcrumb at all
  if (location.pathname === '/generate') {
    return null; // Don't show breadcrumbs on generator page
  } else {
    // For all other routes, start with Generate instead of Home
    breadcrumbItems.push({
      label: 'Generate',
      href: '/generate',
      icon: null
    });

    // Add intermediate paths
    pathnames.forEach((segment, index) => {
      const href = `/${pathnames.slice(0, index + 1).join('/')}`;
      const isLast = index === pathnames.length - 1;

      breadcrumbItems.push({
        label: getLabel(segment),
        href: isLast ? null : href, // Don't make the last item clickable
        icon: null
      });
    });
  }

  return (
    <nav className={`flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400 mb-6 ${className}`} aria-label="Breadcrumb">
      <ol className="flex items-center space-x-2 flex-wrap">
        {breadcrumbItems.map((crumb, index) => (
          <li key={index} className="flex items-center">
            {index > 0 && <ChevronRight className="w-4 h-4 mx-2 text-gray-400 flex-shrink-0" />}
            {crumb.href ? (
              <Link 
                to={crumb.href} 
                className="hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 flex items-center hover:underline"
              >
                {crumb.icon && <crumb.icon className="w-4 h-4 mr-1 flex-shrink-0" />}
                <span className="truncate max-w-[150px] sm:max-w-none">{crumb.label}</span>
              </Link>
            ) : (
              <span className="text-gray-900 dark:text-white font-medium flex items-center">
                {crumb.icon && <crumb.icon className="w-4 h-4 mr-1 flex-shrink-0" />}
                <span className="truncate max-w-[150px] sm:max-w-none">{crumb.label}</span>
              </span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
};

// Structured data for SEO
export const BreadcrumbStructuredData = ({ breadcrumbs }) => {
  if (!breadcrumbs || breadcrumbs.length === 0) return null;

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbs.map((crumb, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": crumb.label,
      "item": crumb.href ? `https://genfreeai.com${crumb.href}` : undefined
    }))
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
};

export default Breadcrumbs;
