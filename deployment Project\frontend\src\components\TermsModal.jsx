import React, { useState, useEffect } from 'react';
import { X, FileText, Check } from 'lucide-react';
import { Link } from 'react-router-dom';

const TermsModal = ({ isOpen, onAccept, onDecline }) => {
  const [hasScrolledToBottom, setHasScrolledToBottom] = useState(false);

  const handleScroll = (e) => {
    const { scrollTop, scrollHeight, clientHeight } = e.target;
    const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10;
    if (isAtBottom) {
      setHasScrolledToBottom(true);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-2xl w-full max-h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3">
            <FileText className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Terms and Conditions
            </h2>
          </div>
          <button
            onClick={onDecline}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
          </button>
        </div>

        {/* Content */}
        <div 
          className="flex-1 overflow-y-auto p-6 space-y-4"
          onScroll={handleScroll}
        >
          <div className="text-gray-700 dark:text-gray-300 space-y-4">
            <p className="font-medium">
              Welcome to our AI Image Generator! Before you start creating amazing images, please review and accept our terms:
            </p>

            <div className="space-y-3">
              <h3 className="font-semibold text-gray-900 dark:text-white">Key Points:</h3>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start gap-2">
                  <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>This service is for personal and educational use</span>
                </li>
                <li className="flex items-start gap-2">
                  <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>You can download and share generated images with attribution</span>
                </li>
                <li className="flex items-start gap-2">
                  <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Do not generate illegal, harmful, or offensive content</span>
                </li>
                <li className="flex items-start gap-2">
                  <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Respect intellectual property rights</span>
                </li>
                <li className="flex items-start gap-2">
                  <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Your prompts are processed to generate images</span>
                </li>
                <li className="flex items-start gap-2">
                  <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Image history is stored locally on your device</span>
                </li>
              </ul>
            </div>

            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
              <p className="text-yellow-800 dark:text-yellow-200 text-sm">
                <strong>Important:</strong> Generated images may resemble existing copyrighted works. 
                You are responsible for ensuring your use complies with applicable laws.
              </p>
            </div>

            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <p className="text-blue-800 dark:text-blue-200 text-sm">
                <strong>Privacy:</strong> We respect your privacy. Your data is processed securely, 
                and we don't store personal information unless explicitly provided.
              </p>
            </div>

            <p className="text-sm text-gray-600 dark:text-gray-400">
              For complete terms and conditions, please visit our{' '}
              <Link 
                to="/terms" 
                className="text-blue-600 dark:text-blue-400 hover:underline"
                onClick={onDecline}
              >
                full terms page
              </Link>.
            </p>

            {!hasScrolledToBottom && (
              <div className="text-center">
                <p className="text-sm text-gray-500 dark:text-gray-400 animate-pulse">
                  Please scroll down to continue...
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex flex-col sm:flex-row gap-3 p-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onDecline}
            className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 
                     hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            Decline
          </button>
          <button
            onClick={onAccept}
            disabled={!hasScrolledToBottom}
            className="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 
                     text-white rounded-lg transition-colors disabled:cursor-not-allowed
                     flex items-center justify-center gap-2"
          >
            <Check className="w-4 h-4" />
            Accept & Continue
          </button>
        </div>
      </div>
    </div>
  );
};

export default TermsModal;
