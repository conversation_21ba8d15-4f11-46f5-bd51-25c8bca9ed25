import React from 'react';
import { Download, X, Gift } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import AdvertisementSpace from './AdvertisementSpace';

const DownloadModal = ({
  isOpen,
  onClose,
  imageUrl,
  prompt
}) => {
  const navigate = useNavigate();

  if (!isOpen) return null;

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleFreeDownload = () => {
    // Navigate to download route using React Router
    const downloadUrl = `/image/download?url=${encodeURIComponent(imageUrl)}&prompt=${encodeURIComponent(prompt || 'generated-image')}`;
    navigate(downloadUrl);
    onClose();
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-[100px] "
      onClick={handleBackdropClick}
    >
      <div
        className="bg-gray-800 rounded-xl shadow-2xl max-w-md w-full h-[100%] mx-4 overflow-auto animate-scale-in "
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-6 text-white relative">
          <button
            onClick={onClose}
            className="absolute top-4 right-4 p-1 hover:bg-white/20 rounded-full transition-colors"
          >
            <X className="w-5 h-5" />
          </button>

          <div className="flex items-center gap-3">
            <div className="p-3 bg-white/20 rounded-full">
              <Download className="w-6 h-6" />
            </div>
            <div>
              <h3 className="text-xl font-bold">Download Image</h3>
              <p className="text-blue-100 text-sm">Get your AI-generated masterpiece</p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Image Preview */}
          {imageUrl && (
            <div className="mb-6">
              <img
                src={imageUrl}
                alt={prompt}
                className="w-full h-32 object-cover rounded-lg shadow-md"
              />
              {prompt && (
                <p className="text-sm text-gray-300 mt-2 line-clamp-2">
                  <span className="font-medium">Prompt:</span> {prompt}
                </p>
              )}
            </div>
          )}

          {/* Download Section */}
          <div className="space-y-6">
            {/* Advertisement Space - Above Everything */}
            <AdvertisementSpace />

            {/* Free Download */}
            <div className="border-2 border-green-700 rounded-lg p-4 bg-green-900/20">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <Gift className="w-5 h-5 text-green-400" />
                  <span className="font-semibold text-green-200">Free Download</span>
                </div>
                <span className="bg-green-800 text-green-200 px-2 py-1 rounded-full text-xs font-medium">
                  $0.00
                </span>
              </div>

              <ul className="text-sm text-green-300 mb-4 space-y-1">
                <li>• Standard quality image</li>
                <li>• JPG format</li>
                <li>• Personal use license</li>
                <li>• Instant download</li>
              </ul>

              <button
                onClick={handleFreeDownload}
                className="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2"
              >
                <Download className="w-5 h-5" />
                Free Download
              </button>
            </div>
          </div>

          {/* Footer */}
          <div className="mt-6 pt-4 border-t border-gray-600">
            <p className="text-xs text-gray-400 text-center">
              By downloading, you agree to our{' '}
              <a href="/terms" className="text-blue-400 hover:underline">
                Terms of Service
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DownloadModal;
