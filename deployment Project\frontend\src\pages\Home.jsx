import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import SEO, { pageSEO } from '../components/SEO';
import Logo from '../components/Logo';
import { Sparkles, Zap, Heart, Star, ArrowRight, Palette, Wand2, Image as ImageIcon, Camera, Users, Award, Shield } from 'lucide-react';

// Shine animation styles and Sparkle button styles
const combinedStyles = `
  @import url('https://fonts.googleapis.com/css2?family=Bungee&family=Oxygen:wght@300;400;700&family=Roboto+Slab:wght@300;400;500;700&display=swap');

  .btn-shine {
    position: relative;
    color: #fff;
    background: linear-gradient(to right, #9f9f9f 0, #fff 10%, #868686 20%);
    background-position: 0;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: shine 4s infinite linear;
    animation-fill-mode: forwards;
    -webkit-text-size-adjust: none;
    text-decoration: none;
    white-space: wrap;
  }

  .oxygen-font {
    font-family: 'Oxygen', sans-serif;
  }

  .roboto-slab-font {
    font-family: 'Roboto Slab', serif;
  }

  /* Sparkle Button Styles */
  .button {
    --black-700: hsla(0 0% 12% / 1);
    --border_radius: 9999px;
    --transtion: 0.3s ease-in-out;
    --offset: 2px;

    cursor: pointer;
    position: relative;

    display: flex;
    align-items: center;
    gap: 0.5rem;

    transform-origin: center;

    padding: 0.5rem 0.5rem;
    background-color: transparent;

    border: none;
    border-radius: var(--border_radius);
    transform: scale(calc(1 + (var(--active, 0) * 0.1)));

    transition: transform var(--transtion);
  }

  .button::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    width: 100%;
    height: 100%;
    background-color: var(--black-700);

    border-radius: var(--border_radius);
    box-shadow: inset 0 0.5px hsl(0, 0%, 100%), inset 0 -1px 2px 0 hsl(0, 0%, 0%),
      0px 4px 10px -4px hsla(0 0% 0% / calc(1 - var(--active, 0))),
      0 0 0 calc(var(--active, 0) * 0.375rem) hsl(260 97% 50% / 0.75);

    transition: all var(--transtion);
    z-index: 0;
  }

  .button::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    width: 100%;
    height: 100%;
    background-color: hsla(260 97% 61% / 0.75);
    background-image: radial-gradient(
        at 51% 89%,
        hsla(266, 45%, 74%, 1) 0px,
        transparent 50%
      ),
      radial-gradient(at 100% 100%, hsla(266, 36%, 60%, 1) 0px, transparent 50%),
      radial-gradient(at 22% 91%, hsla(266, 36%, 60%, 1) 0px, transparent 50%);
    background-position: top;

    opacity: var(--active, 0);
    border-radius: var(--border_radius);
    transition: opacity var(--transtion);
    z-index: 2;
  }

  .button:is(:hover, :focus-visible) {
    --active: 1;
  }
  .button:active {
    transform: scale(1);
  }

  .button .dots_border {
    --size_border: calc(100% + 2px);

    overflow: hidden;

    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    width: var(--size_border);
    height: var(--size_border);
    background-color: transparent;

    border-radius: var(--border_radius);
    z-index: -10;
  }

  .button .dots_border::before {
    content: "";
    position: absolute;
    top: 30%;
    left: 50%;
    transform: translate(-50%, -50%);
    transform-origin: left;
    transform: rotate(0deg);

    width: 100%;
    height: 2rem;
    background-color: white;

    mask: linear-gradient(transparent 0%, white 120%);
    animation: rotate 2s linear infinite;
  }

  @keyframes rotate {
    to {
      transform: rotate(360deg);
    }
  }

  .button .sparkle {
    position: relative;
    z-index: 10;

    width: 1.75rem;
  }

  .button .sparkle .path {
    fill: currentColor;
    stroke: currentColor;

    transform-origin: center;

    color: hsl(0, 0%, 100%);
  }

  .button:is(:hover, :focus) .sparkle .path {
    animation: path 1.5s linear 0.5s infinite;
  }

  .button .sparkle .path:nth-child(1) {
    --scale_path_1: 1.2;
  }
  .button .sparkle .path:nth-child(2) {
    --scale_path_2: 1.2;
  }
  .button .sparkle .path:nth-child(3) {
    --scale_path_3: 1.2;
  }

  @keyframes path {
    0%,
    34%,
    71%,
    100% {
      transform: scale(1);
    }
    17% {
      transform: scale(var(--scale_path_1, 1));
    }
    49% {
      transform: scale(var(--scale_path_2, 1));
    }
    83% {
      transform: scale(var(--scale_path_3, 1));
    }
  }

  .button .text_button {
    position: relative;
    z-index: 10;

    background-image: linear-gradient(
      90deg,
      hsla(0 0% 100% / 1) 0%,
      hsla(0 0% 100% / var(--active, 0)) 120%
    );
    background-clip: text;

    font-size: 1.5rem;
    font-weight: bold;
    color: transparent;
  }

  /* Shine animation keyframes */
  @-moz-keyframes shine {
    0% {
      background-position: 0;
    }
    60% {
      background-position: 680px;
    }
    100% {
      background-position: 1200px;
    }
  }
  @-webkit-keyframes shine {
    0% {
      background-position: 0;
    }
    60% {
      background-position: 680px;
    }
    100% {
      background-position: 1200px;
    }
  }
  @-o-keyframes shine {
    0% {
      background-position: 0;
    }
    60% {
      background-position: 580px;
    }
    100% {
      background-position: 1100px;
    }
  }
  @keyframes shine {
    0% {
      background-position: 0;
    }
    60% {
      background-position: 580px;
    }
    100% {
      background-position: 1100px;
    }
  }
`;

// Welcome Screen Component
const WelcomeScreen = () => {
  const navigate = useNavigate();

  const handleGetStarted = () => {
    navigate('/generate');
  };

  return (
    <div className="min-h-screen">
      {/* Add shine animation and sparkle button styles */}
      <style dangerouslySetInnerHTML={{ __html: combinedStyles }} />

      {/* Hero Section */}
      <div className="max-w-6xl mx-auto px-4 pt-8 pb-16">
        <div className="text-center">
          {/* Main Title */}
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 animate-fade-in-up">
            <span className="btn-shine">Create Hight Quality
            Free Images </span>
             
            
            <span className="bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent">using AI</span>
          </h1>

          {/* Subtitle */}
          <h2 className="text-2xl md:text-3xl lg:text-4xl font-semibold text-white mt-10 mb-8 animate-fade-in-up oxygen-font" style={{ animationDelay: '0s' }}>
           Stop Paying  <span className="bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent">$100</span> for Images. Use Our Free Text to Image AI Tool
          </h2>

          {/* Description */}
          <p className="text-lg md:text-xl text-gray-300 mb-12 max-w-4xl mx-auto leading-relaxed animate-fade-in-up roboto-slab-font" style={{ animationDelay: '0.0s' }}>
           Want to make AI images for free? Try GenFreeAI, a powerful text to image AI tool that turns your prompts into masterpieces. It's a text-to-image image generator, completely free, with no signup, no hidden fees, and no watermark. 
          </p>

          {/* CTA Button */}
          <div className="mb-10 animate-fade-in-up flex justify-center" style={{ animationDelay: '0s' }}>
            <button
              onClick={handleGetStarted}
              className="button"
            >
              <div className="dots_border"></div>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                className="sparkle"
              >
                <path
                  className="path"
                  strokeLinejoin="round"
                  strokeLinecap="round"
                  stroke="black"
                  fill="black"
                  d="M14.187 8.096L15 5.25L15.813 8.096C16.0231 8.83114 16.4171 9.50062 16.9577 10.0413C17.4984 10.5819 18.1679 10.9759 18.903 11.186L21.75 12L18.904 12.813C18.1689 13.0231 17.4994 13.4171 16.9587 13.9577C16.4181 14.4984 16.0241 15.1679 15.814 15.903L15 18.75L14.187 15.904C13.9769 15.1689 13.5829 14.4994 13.0423 13.9587C12.5016 13.4181 11.8321 13.0241 11.097 12.814L8.25 12L11.096 11.187C11.8311 10.9769 12.5006 10.5829 13.0413 10.0423C13.5819 9.50162 13.9759 8.83214 14.186 8.097L14.187 8.096Z"
                ></path>
                <path
                  className="path"
                  strokeLinejoin="round"
                  strokeLinecap="round"
                  stroke="black"
                  fill="black"
                  d="M6 14.25L5.741 15.285C5.59267 15.8785 5.28579 16.4206 4.85319 16.8532C4.42059 17.2858 3.87853 17.5927 3.285 17.741L2.25 18L3.285 18.259C3.87853 18.4073 4.42059 18.7142 4.85319 19.1468C5.28579 19.5794 5.59267 20.1215 5.741 20.715L6 21.75L6.259 20.715C6.40725 20.1216 6.71398 19.5796 7.14639 19.147C7.5788 18.7144 8.12065 18.4075 8.714 18.259L9.75 18L8.714 17.741C8.12065 17.5925 7.5788 17.2856 7.14639 16.853C6.71398 16.4204 6.40725 15.8784 6.259 15.285L6 14.25Z"
                ></path>
                <path
                  className="path"
                  strokeLinejoin="round"
                  strokeLinecap="round"
                  stroke="black"
                  fill="black"
                  d="M6.5 4L6.303 4.5915C6.24777 4.75718 6.15472 4.90774 6.03123 5.03123C5.90774 5.15472 5.75718 5.24777 5.5915 5.303L5 5.5L5.5915 5.697C5.75718 5.75223 5.90774 5.84528 6.03123 5.96877C6.15472 6.09226 6.24777 6.24282 6.303 6.4085L6.5 7L6.697 6.4085C6.75223 6.24282 6.84528 6.09226 6.96877 5.96877C7.09226 5.84528 7.24282 5.75223 7.4085 5.697L8 5.5L7.4085 5.303C7.24282 5.24777 7.09226 5.15472 6.96877 5.03123C6.84528 4.90774 6.75223 4.75718 6.697 4.5915L6.5 4Z"
                ></path>
              </svg>
              <span className="text_button">Generate Now</span>
            </button>
          </div>

          {/* Laptop Image */}
          <div className="mb-10 animate-fade-in-up" style={{ animationDelay: '0s' }}>
            <div className="max-w-4xl mx-auto">
              <img
                src="https://ik.imagekit.io/q0mafimea/Untitled%20design%20(25).png?updatedAt=1751996702434"
                alt="AI Image Generator Interface"
                className="w-full h-auto rounded-2xl shadow-2xl hover:shadow-3xl transition-all duration-500 hover:scale-105"
              />
            </div>
          </div>

        </div>
      </div>

      {/* Gaming Avatar Section */}
      <div className="bg-gradient-to-br from-purple-900/20 via-blue-900/20 to-indigo-900/20 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 animate-fade-in-up">
              <span className="bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 bg-clip-text text-transparent">
                Become the Star of Your Own Universe
              </span>
            </h2>
          </div>

          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Side - Image */}
            <div className="order-1 lg:order-1 animate-fade-in-up" style={{ animationDelay: '0s' }}>
              <div className="relative group">
                <div className="absolute -inset-4 bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 rounded-3xl blur-xl opacity-30 group-hover:opacity-50 transition-opacity duration-500"></div>
                <div className="relative bg-gradient-to-br from-purple-900/50 to-pink-900/50 p-4 rounded-3xl backdrop-blur-sm border border-purple-500/30">
                  <img
                    src="https://ik.imagekit.io/q0mafimea/imag%20(1).png?updatedAt=1751996645288"
                    alt="Gaming Avatar Creation"
                    className="w-full h-auto rounded-2xl shadow-2xl hover:scale-105 transition-all duration-500"
                  />
                </div>
              </div>
            </div>

            {/* Right Side - Content */}
            <div className="order-2 lg:order-2 animate-fade-in-up" style={{ animationDelay: '0s' }}>
              <div className="space-y-8">
                <div>
                  <h3 className="text-3xl md:text-4xl font-bold text-white mb-4">
                    Gaming Avatars
                  </h3>
                  <p className="text-xl md:text-2xl text-purple-300 font-semibold mb-6">
                    Bring Your In-Game Identity to Life
                  </p>
                  <p className="text-lg text-gray-300 leading-relaxed mb-8">
                    Create unique, high-quality gaming avatars using our Free Text-to-Image Generator — no design skills needed!
                  </p>
                </div>

                <div className="space-y-6">
                  <div className="flex items-start gap-4 group">
                    <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <span className="text-white font-bold">🔹</span>
                    </div>
                    <p className="text-gray-300 text-lg leading-relaxed">
                      <span className="text-white font-semibold">Customize your look</span> with detailed prompts and unique styles
                    </p>
                  </div>

                  <div className="flex items-start gap-4 group">
                    <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <span className="text-white font-bold">🔹</span>
                    </div>
                    <p className="text-gray-300 text-lg leading-relaxed">
                      <span className="text-white font-semibold">Perfect for streamers, gamers,</span> and online profiles
                    </p>
                  </div>

                  <div className="flex items-start gap-4 group">
                    <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <span className="text-white font-bold">🔹</span>
                    </div>
                    <p className="text-gray-300 text-lg leading-relaxed">
                      <span className="text-white font-semibold">Stand out in any game world</span> with AI-powered avatar creation
                    </p>
                  </div>
                </div>

                <div className="pt-8">
                  <button
                    onClick={handleGetStarted}
                    className="group inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white text-lg font-bold rounded-full shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300"
                  >
                    <Wand2 className="w-6 h-6 group-hover:rotate-12 transition-transform duration-300" />
                    <span>Create Now</span>
                    <ArrowRight className="w-6 h-6 group-hover:translate-x-1 transition-transform duration-300" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Cyberpunk Section */}
      <div className="bg-gradient-to-br from-cyan-900/20 via-teal-900/20 to-green-900/20 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Side - Content */}
            <div className="order-2 lg:order-1 animate-fade-in-up" style={{ animationDelay: '0s' }}>
              <div className="space-y-8">
                <div>
                  <h3 className="text-3xl md:text-4xl font-bold text-white mb-4">
                    Cyberpunk Images
                  </h3>
                  <p className="text-xl md:text-2xl text-cyan-300 font-semibold mb-6">
                    Step into a Neon-Fueled Future
                  </p>
                  <p className="text-lg text-gray-300 leading-relaxed mb-8">
                    Explore the dark, gritty, and electrifying world of cyberpunk with our high-quality AI-generated images. From glowing cityscapes to futuristic characters, each image brings the cyberpunk aesthetic to life with stunning detail.
                  </p>
                </div>

                <div className="space-y-6">
                  <div className="flex items-start gap-4 group">
                    <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-cyan-500 to-teal-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <span className="text-white font-bold">🔹</span>
                    </div>
                    <p className="text-gray-300 text-lg leading-relaxed">
                      <span className="text-white font-semibold">Perfect for game developers, digital artists,</span> and cyberpunk lovers
                    </p>
                  </div>

                  <div className="flex items-start gap-4 group">
                    <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-cyan-500 to-teal-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <span className="text-white font-bold">🔹</span>
                    </div>
                    <p className="text-gray-300 text-lg leading-relaxed">
                      <span className="text-white font-semibold">Visualize your future self</span> in a dystopian tech world
                    </p>
                  </div>

                  <div className="flex items-start gap-4 group">
                    <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-cyan-500 to-teal-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <span className="text-white font-bold">🔹</span>
                    </div>
                    <p className="text-gray-300 text-lg leading-relaxed">
                      <span className="text-white font-semibold">Live the vibe</span> — bring your cyberpunk dreams to life in just minutes
                    </p>
                  </div>
                </div>

                <div className="pt-8">
                  <button
                    onClick={handleGetStarted}
                    className="group inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-cyan-600 to-teal-600 hover:from-cyan-700 hover:to-teal-700 text-white text-lg font-bold rounded-full shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300"
                  >
                    <Wand2 className="w-6 h-6 group-hover:rotate-12 transition-transform duration-300" />
                    <span>Create Now</span>
                    <ArrowRight className="w-6 h-6 group-hover:translate-x-1 transition-transform duration-300" />
                  </button>
                </div>
              </div>
            </div>

            {/* Right Side - Image */}
            <div className="order-1 lg:order-2 animate-fade-in-up" style={{ animationDelay: '0s' }}>
              <div className="relative group">
                <div className="absolute -inset-4 bg-gradient-to-r from-cyan-600 via-teal-600 to-green-600 rounded-3xl blur-xl opacity-30 group-hover:opacity-50 transition-opacity duration-500"></div>
                <div className="relative bg-gradient-to-br from-cyan-900/50 to-teal-900/50 p-4 rounded-3xl backdrop-blur-sm border border-cyan-500/30">
                  <img
                    src="https://ik.imagekit.io/q0mafimea/Untitled%20design%20(33)%20(1).png?updatedAt=1751996645503"
                    alt="Cyberpunk AI Generated Images"
                    className="w-full h-auto rounded-2xl shadow-2xl hover:scale-105 transition-all duration-500"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Models Section */}
      <div className="bg-gradient-to-br from-rose-900/20 via-pink-900/20 to-purple-900/20 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Side - Image */}
            <div className="order-1 lg:order-1 animate-fade-in-up" style={{ animationDelay: '0s' }}>
              <div className="relative group">
                <div className="absolute -inset-4 bg-gradient-to-r from-rose-600 via-pink-600 to-purple-600 rounded-3xl blur-xl opacity-30 group-hover:opacity-50 transition-opacity duration-500"></div>
                <div className="relative bg-gradient-to-br from-rose-900/50 to-pink-900/50 p-4 rounded-3xl backdrop-blur-sm border border-rose-500/30">
                  <img
                    src="https://ik.imagekit.io/q0mafimea/Untitled%20design%20(32)%20(1)%20(1).png?updatedAt=1751996675303"
                    alt="Gaming Avatar Models"
                    className="w-full h-auto rounded-2xl shadow-2xl hover:scale-105 transition-all duration-500"
                  />
                </div>
              </div>
            </div>

            {/* Right Side - Content */}
            <div className="order-2 lg:order-2 animate-fade-in-up" style={{ animationDelay: '0s' }}>
              <div className="space-y-8">
                <div>
                  <h3 className="text-3xl md:text-4xl font-bold text-white mb-4">
                    Professional Headshots

                  </h3>
                  <p className="text-xl md:text-2xl text-rose-300 font-semibold mb-6">
                    Bring Your Online Identity to Life
                  </p>
                  <p className="text-lg text-gray-300 leading-relaxed mb-8">
                    Create your professional models with our Free AI Image Generator. 
Perfect for designer , gamier and Developer

                  </p>
                </div>

                <div className="space-y-6">
                  <div className="flex items-start gap-4 group">
                    <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-rose-500 to-pink-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <span className="text-white font-bold">🔹</span>
                    </div>
                    <p className="text-gray-300 text-lg leading-relaxed">
                      <span className="text-white font-semibold">Professional images</span> Create Professional images for your business 
                    </p>
                  </div>

                  <div className="flex items-start gap-4 group">
                    <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-rose-500 to-pink-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <span className="text-white font-bold">🔹</span>
                    </div>
                    <p className="text-gray-300 text-lg leading-relaxed">
                      <span className="text-white font-semibold">Perfect for ,</span>  Designer, Entrepreneurs and Digital Marketing

                    </p>
                  </div>

                  <div className="flex items-start gap-4 group">
                    <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-rose-500 to-pink-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <span className="text-white font-bold">🔹</span>
                    </div>
                    <p className="text-gray-300 text-lg leading-relaxed">
                      <span className="text-white font-semibold">Save </span> hundreds of dollars hundreds of dollars 
                    </p>
                  </div>
                </div>

                <div className="pt-8">
                  <button
                    onClick={handleGetStarted}
                    className="group inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-rose-600 to-pink-600 hover:from-rose-700 hover:to-pink-700 text-white text-lg font-bold rounded-full shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300"
                  >
                    <Wand2 className="w-6 h-6 group-hover:rotate-12 transition-transform duration-300" />
                    <span>Create Now</span>
                    <ArrowRight className="w-6 h-6 group-hover:translate-x-1 transition-transform duration-300" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="max-w-6xl mx-auto px-4 py-16">
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div className="text-center group">
            <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
              <Camera className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">Professional Portraits</h3>
            <p className="text-gray-400">Perfect for LinkedIn, resumes, and business profiles</p>
          </div>

          <div className="text-center group">
            <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
              <Users className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">Social Media Ready</h3>
            <p className="text-gray-400">Instagram, Tinder, and dating app profile pictures</p>
          </div>

          <div className="text-center group">
            <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-pink-500 to-red-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
              <Award className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">High Quality</h3>
            <p className="text-gray-400">Professional-grade AI generated images</p>
          </div>

          <div className="text-center group">
            <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-green-500 to-blue-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
              <Shield className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">100% Free</h3>
            <p className="text-gray-400">No signup required, unlimited generations</p>
          </div>
        </div>
      </div>

      {/* How It Works Section */}
      <div className="max-w-6xl mx-auto px-4 py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">How It Works</h2>
          <p className="text-xl text-gray-300">Create professional headshots in 3 simple steps</p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="w-12 h-12 mx-auto mb-4 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-xl">
              1
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">Describe Your Vision</h3>
            <p className="text-gray-400">Tell our AI what kind of headshot you want - professional, casual, creative, or themed</p>
          </div>

          <div className="text-center">
            <div className="w-12 h-12 mx-auto mb-4 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center text-white font-bold text-xl">
              2
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">AI Creates Magic</h3>
            <p className="text-gray-400">Our advanced AI processes your request and generates stunning, realistic portraits</p>
          </div>

          <div className="text-center">
            <div className="w-12 h-12 mx-auto mb-4 bg-gradient-to-r from-pink-500 to-red-600 rounded-full flex items-center justify-center text-white font-bold text-xl">
              3
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">Download & Use</h3>
            <p className="text-gray-400">Get your high-resolution headshots instantly and use them anywhere you need</p>
          </div>
        </div>
      </div>

    </div>
  );
};

const Home = () => {
  useEffect(() => {
    // Scroll to top when page loads
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  return (
    <div className="relative min-h-screen">
      {/* SEO */}
      <SEO
        title={pageSEO.home.title}
        description={pageSEO.home.description}
        keywords={pageSEO.home.keywords}
      />

      {/* Background gradient - Dark theme like PhotoGPT */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-black -z-10"></div>

      {/* Subtle pattern overlay */}
      <div className="absolute inset-0 opacity-10 -z-5" style={{
        backgroundImage: `radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.3) 0%, transparent 50%),
                         radial-gradient(circle at 75% 75%, rgba(147, 51, 234, 0.3) 0%, transparent 50%)`
      }}></div>

      {/* Content container */}
      <div className="relative z-10">
        <WelcomeScreen />
      </div>
    </div>
  );
};

export default Home;
