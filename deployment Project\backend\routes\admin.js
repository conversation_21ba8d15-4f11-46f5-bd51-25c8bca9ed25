const express = require('express');
const router = express.Router();
const Image = require('../models/Image');

// Test endpoint
router.get('/test', (req, res) => {
  res.json({
    success: true,
    message: 'Admin API is working with MongoDB Atlas!',
    timestamp: new Date().toISOString()
  });
});

// Get all images with filters
router.get('/images', async (req, res) => {
  try {
    const {
      aspect_ratio,
      category,
      time_filter,
      page = 1,
      limit = 20,
      sort = 'createdAt',
      order = 'desc'
    } = req.query;

    // Build filter object
    const filter = {};

    // Aspect ratio filter
    if (aspect_ratio && aspect_ratio !== 'all') {
      filter.aspectRatio = aspect_ratio;
    }

    // Category filter (search in prompt)
    if (category && category !== 'all') {
      const categoryKeywords = {
        't-shirt': ['t-shirt', 'tshirt', 'shirt', 'clothing', 'apparel'],
        'logo': ['logo', 'brand', 'emblem', 'symbol', 'icon'],
        'people': ['people', 'person', 'human', 'man', 'woman', 'child', 'face'],
        'nature': ['nature', 'landscape', 'tree', 'forest', 'mountain', 'ocean', 'sky'],
        'poster': ['poster', 'banner', 'advertisement', 'flyer', 'design']
      };

      const keywords = categoryKeywords[category] || [category];
      filter.prompt = {
        $regex: keywords.join('|'),
        $options: 'i'
      };
    }

    // Time filter
    if (time_filter && time_filter !== 'all') {
      const now = new Date();
      let startDate;

      switch (time_filter) {
        case 'day':
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
      }

      if (startDate) {
        filter.createdAt = { $gte: startDate };
      }
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sortOrder = order === 'desc' ? -1 : 1;

    // Get images with pagination
    const images = await Image.find(filter)
      .sort({ [sort]: sortOrder })
      .skip(skip)
      .limit(parseInt(limit))
      .lean();

    // Get total count for pagination
    const totalImages = await Image.countDocuments(filter);
    const totalPages = Math.ceil(totalImages / parseInt(limit));

    // Get statistics
    const stats = await getImageStats(filter);

    res.json({
      success: true,
      data: {
        images,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalImages,
          hasNext: parseInt(page) < totalPages,
          hasPrev: parseInt(page) > 1
        },
        stats
      }
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to fetch images'
    });
  }
});

// Get image statistics
async function getImageStats(baseFilter = {}) {
  try {
    const [
      totalCount,
      todayCount,
      weekCount,
      monthCount,
      aspectRatioStats,
      categoryStats
    ] = await Promise.all([
      // Total images
      Image.countDocuments(baseFilter),

      // Today's images
      Image.countDocuments({
        ...baseFilter,
        createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
      }),

      // This week's images
      Image.countDocuments({
        ...baseFilter,
        createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }
      }),

      // This month's images
      Image.countDocuments({
        ...baseFilter,
        createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
      }),

      // Aspect ratio distribution
      Image.aggregate([
        { $match: baseFilter },
        { $group: { _id: '$aspectRatio', count: { $sum: 1 } } },
        { $sort: { count: -1 } }
      ]),

      // Category estimation (basic keyword matching)
      Image.aggregate([
        { $match: baseFilter },
        {
          $project: {
            prompt: 1,
            category: {
              $switch: {
                branches: [
                  {
                    case: { $regexMatch: { input: '$prompt', regex: /t-shirt|tshirt|shirt|clothing|apparel/i } },
                    then: 't-shirt'
                  },
                  {
                    case: { $regexMatch: { input: '$prompt', regex: /logo|brand|emblem|symbol|icon/i } },
                    then: 'logo'
                  },
                  {
                    case: { $regexMatch: { input: '$prompt', regex: /people|person|human|man|woman|child|face/i } },
                    then: 'people'
                  },
                  {
                    case: { $regexMatch: { input: '$prompt', regex: /nature|landscape|tree|forest|mountain|ocean|sky/i } },
                    then: 'nature'
                  },
                  {
                    case: { $regexMatch: { input: '$prompt', regex: /poster|banner|advertisement|flyer|design/i } },
                    then: 'poster'
                  }
                ],
                default: 'other'
              }
            }
          }
        },
        { $group: { _id: '$category', count: { $sum: 1 } } },
        { $sort: { count: -1 } }
      ])
    ]);

    return {
      total: totalCount,
      today: todayCount,
      week: weekCount,
      month: monthCount,
      aspectRatios: aspectRatioStats,
      categories: categoryStats
    };
  } catch (error) {
    return {
      total: 0,
      today: 0,
      week: 0,
      month: 0,
      aspectRatios: [],
      categories: []
    };
  }
}

// Delete image (admin only)
router.delete('/images/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const deletedImage = await Image.findByIdAndDelete(id);

    if (!deletedImage) {
      return res.status(404).json({
        success: false,
        error: 'Image not found'
      });
    }

    res.json({
      success: true,
      message: 'Image deleted successfully'
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to delete image'
    });
  }
});

// Get dashboard stats
router.get('/dashboard', async (req, res) => {
  try {
    const stats = await getImageStats();

    // Get recent images
    const recentImages = await Image.find()
      .sort({ createdAt: -1 })
      .limit(10)
      .lean();

    res.json({
      success: true,
      data: {
        stats,
        recentImages
      }
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to fetch dashboard data'
    });
  }
});

module.exports = router;
