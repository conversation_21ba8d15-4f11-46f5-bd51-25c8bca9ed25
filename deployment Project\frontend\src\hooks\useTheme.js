import { useEffect } from 'react';

export const useTheme = () => {
  // Always use dark theme
  const theme = 'dark';

  useEffect(() => {
    const applyDarkTheme = () => {
      const root = document.documentElement;
      root.classList.add('dark');
      root.classList.remove('light');
      // Always save dark theme to localStorage
      localStorage.setItem('theme', 'dark');
    };

    applyDarkTheme();
  }, []);

  return {
    theme,
    isDark: true,
    isLight: false,
  };
};
