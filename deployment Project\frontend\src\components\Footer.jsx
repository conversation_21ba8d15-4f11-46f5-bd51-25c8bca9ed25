import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import Logo from './Logo';
import StyledSocialIcons from './StyledSocialIcons';
import { 
  Mail, 
  MapPin, 
  Phone, 
  Camera, 
  Wand2, 
  History, 
  Info, 
  HelpCircle, 
  FileText, 
  BookOpen,
  Shield,
  Heart,
  Star,
  Users,
  Award,
  Zap
} from 'lucide-react';

const Footer = () => {
  const navigate = useNavigate();

  const handleTermsClick = () => {
    navigate('/terms');
  };

  const quickLinks = [
    { name: 'Generate Images', path: '/generate', icon: Wand2 },
    { name: 'Image History', path: '/history', icon: History },
    { name: 'About Us', path: '/about', icon: Info },
    { name: 'FAQ', path: '/faq', icon: HelpCircle },
    // { name: 'Blog', path: '/blog', icon: BookOpen },
  ];

  const features = [
    { name: 'Professional Portraits', icon: Camera },
    { name: 'Business Headshots', icon: Users },
    { name: 'Social Media Photos', icon: Star },
    { name: 'Creative Images', icon: Wand2 },
    { name: 'High Quality AI', icon: Award },
    { name: 'Lightning Fast', icon: Zap },
  ];



  return (
    <footer className="bg-gradient-to-b from-gray-900 to-black border-t border-gray-800">
      {/* Main Footer Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
          
          {/* Company Info */}
          <div className="lg:col-span-1">
            <div className="mb-6">
              <Logo size="large" />
            </div>
            <p className="text-gray-300 mb-6 leading-relaxed">
              AI-powered tool designed to craft perfect professional and casual headshots.
              Ideal for resume photos, Instagram profile pictures, Tinder images, and beyond.
            </p>
            <div className="flex items-center gap-2 text-sm text-gray-400 mb-4">
              <Shield className="w-4 h-4 text-green-500" />
              <span>100% Free & Secure</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-400">
              <Heart className="w-4 h-4 text-red-500" />
              <span>No signup required</span>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold text-white mb-6">Quick Links</h3>
            <ul className="space-y-3">
              {quickLinks.map((link) => {
                const Icon = link.icon;
                return (
                  <li key={link.path}>
                    <Link
                      to={link.path}
                      className="flex items-center gap-2 text-gray-300 hover:text-white transition-colors duration-200 group"
                    >
                      <Icon className="w-4 h-4 group-hover:scale-110 transition-transform duration-200" />
                      {link.name}
                    </Link>
                  </li>
                );
              })}
            </ul>
          </div>

          {/* Features */}
          <div>
            <h3 className="text-lg font-semibold text-white mb-6">Features</h3>
            <ul className="space-y-3">
              {features.map((feature) => {
                const Icon = feature.icon;
                return (
                  <li key={feature.name} className="flex items-center gap-2 text-gray-300">
                    <Icon className="w-4 h-4 text-blue-400" />
                    {feature.name}
                  </li>
                );
              })}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-semibold text-white mb-6">Contact</h3>
            <div className="space-y-4">
              <div className="flex items-center gap-3 text-gray-400">
                <Mail className="w-5 h-5 text-blue-400" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center gap-3 text-gray-400">
                <Shield className="w-5 h-5 text-green-400" />
                <span>24/7 Support Available</span>
              </div>
            </div>
          </div>
        </div>

       
       
      </div>

      {/* Bottom Bar */}
      <div className="bg-black border-t border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="flex flex-col md:flex-row items-center gap-4 text-sm text-gray-400">
              <span>GenFreeAI</span>
              <div className="flex items-center gap-4">
                <button
                  onClick={handleTermsClick}
                  className="hover:text-gray-300 transition-colors cursor-pointer"
                >
                  Terms & Conditions
                </button>
                <span>•</span>
                <Link to="/about" className="hover:text-gray-300 transition-colors">
                  Privacy Policy
                </Link>
               
              </div>
            </div>
            <StyledSocialIcons compact={true} />
            <div className="text-sm text-gray-400">
              <span className="bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent font-semibold">
                Powered by Flux
              </span>
            </div>
          </div>

          {/* Additional Footer Info */}
          <div className="mt-4 pt-4 border-t border-gray-800 text-center">
            <p className="text-gray-500 text-xs leading-relaxed">
            © 2025 Gen Free AI. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
