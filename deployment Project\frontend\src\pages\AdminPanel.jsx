import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Filter,
  Calendar,
  Image as ImageIcon,
  Trash2,
  Download,
  Eye,
  Search,
  BarChart3,
  Users,
  Clock,
  Grid3X3,
  Globe,
  Smartphone,
  Monitor,
  Tablet
} from 'lucide-react';


const AdminPanel = () => {
  const [images, setImages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({});
  const [filters, setFilters] = useState({
    aspect_ratio: 'all',
    category: 'all',
    time_filter: 'all',
    page: 1
  });
  const [pagination, setPagination] = useState({});
  const [selectedImage, setSelectedImage] = useState(null);
  const [countryData, setCountryData] = useState({});
  // Removed download modal states

  const navigate = useNavigate();

  // Utility function to parse device info from user agent
  const parseDeviceInfo = (userAgent) => {
    if (!userAgent) return { device: 'Unknown', browser: 'Unknown', os: 'Unknown' };

    const ua = userAgent.toLowerCase();

    // Device detection
    let device = 'Desktop';
    let deviceIcon = Monitor;
    if (ua.includes('mobile') || ua.includes('android') || ua.includes('iphone')) {
      device = 'Mobile';
      deviceIcon = Smartphone;
    } else if (ua.includes('tablet') || ua.includes('ipad')) {
      device = 'Tablet';
      deviceIcon = Tablet;
    }

    // Browser detection
    let browser = 'Unknown';
    if (ua.includes('chrome') && !ua.includes('edg')) browser = 'Chrome';
    else if (ua.includes('firefox')) browser = 'Firefox';
    else if (ua.includes('safari') && !ua.includes('chrome')) browser = 'Safari';
    else if (ua.includes('edg')) browser = 'Edge';
    else if (ua.includes('opera')) browser = 'Opera';

    // OS detection
    let os = 'Unknown';
    if (ua.includes('windows')) os = 'Windows';
    else if (ua.includes('mac')) os = 'macOS';
    else if (ua.includes('linux')) os = 'Linux';
    else if (ua.includes('android')) os = 'Android';
    else if (ua.includes('ios') || ua.includes('iphone') || ua.includes('ipad')) os = 'iOS';

    return { device, browser, os, deviceIcon };
  };

  // Function to get country from IP address
  const getCountryFromIP = async (ipAddress) => {
    // Handle local/private IPs
    if (!ipAddress ||
        ipAddress === '::1' ||
        ipAddress === '127.0.0.1' ||
        ipAddress.includes('::ffff:127.0.0.1') ||
        ipAddress.includes('::ffff:192.168.') ||
        ipAddress.includes('::ffff:10.') ||
        ipAddress.includes('::ffff:172.')) {
      return { country: 'Local', countryCode: 'LO', flag: '🏠' };
    }

    // Check if we already have this IP's data
    if (countryData[ipAddress]) {
      return countryData[ipAddress];
    }

    try {
      // Clean IPv6 mapped IPv4 addresses
      let cleanIP = ipAddress.replace('::ffff:', '');

      // Skip if it's still a local/private IP after cleaning
      if (cleanIP.startsWith('192.168.') ||
          cleanIP.startsWith('10.') ||
          cleanIP.startsWith('172.') ||
          cleanIP === '127.0.0.1') {
        const localInfo = { country: 'Local Network', countryCode: 'LO', flag: '🏠' };
        setCountryData(prev => ({ ...prev, [ipAddress]: localInfo }));
        return localInfo;
      }

      // Use a CORS-friendly geolocation service with timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      const response = await fetch(`https://api.country.is/${cleanIP}`, {
        signal: controller.signal,
        headers: {
          'Accept': 'application/json',
        }
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();

      const countryInfo = {
        country: data.country || 'Unknown',
        countryCode: data.country || 'UN',
        flag: data.country ? `https://flagcdn.com/16x12/${data.country.toLowerCase()}.png` : '🌍'
      };

      // Cache the result
      setCountryData(prev => ({
        ...prev,
        [ipAddress]: countryInfo
      }));

      return countryInfo;
    } catch (error) {
      // Don't log errors for aborted requests or common network issues
      if (error.name !== 'AbortError') {
        console.warn('Could not fetch country data for IP:', ipAddress);
      }

      const fallbackInfo = { country: 'Unknown', countryCode: 'UN', flag: '🌍' };
      setCountryData(prev => ({ ...prev, [ipAddress]: fallbackInfo }));
      return fallbackInfo;
    }
  };

  // Fetch images with filters
  const fetchImages = async () => {
    setLoading(true);
    try {
      const queryParams = new URLSearchParams(filters);
      

      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/admin/images?${queryParams}`);

      const data = await response.json();

      if (data.success) {
        setImages(data.data.images);
        setPagination(data.data.pagination);
        setStats(data.data.stats);
      }
    } catch (error) {
      // Error fetching images
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchImages();
  }, [filters]);

  // Fetch country data for all images when images change (with rate limiting)
  useEffect(() => {
    const fetchCountryData = async () => {
      const uniqueIPs = [...new Set(images.map(img => img.ipAddress).filter(ip => ip))];
      const uncachedIPs = uniqueIPs.filter(ip => !countryData[ip]);

      // Limit to 5 requests at a time to avoid rate limiting
      const batchSize = 5;
      for (let i = 0; i < uncachedIPs.length; i += batchSize) {
        const batch = uncachedIPs.slice(i, i + batchSize);

        // Process batch with delay between requests
        const promises = batch.map((ip, index) =>
          new Promise(resolve =>
            setTimeout(() => resolve(getCountryFromIP(ip)), index * 200) // 200ms delay between requests
          )
        );

        await Promise.all(promises);

        // Wait 1 second between batches
        if (i + batchSize < uncachedIPs.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    };

    if (images.length > 0) {
      fetchCountryData();
    }
  }, [images]);

  // Handle filter changes
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1 // Reset to first page when filters change
    }));
  };

  // Handle pagination
  const handlePageChange = (newPage) => {
    setFilters(prev => ({
      ...prev,
      page: newPage
    }));
  };

  // Handle download click
  const handleDownloadClick = (image) => {
    // Navigate to download page using React Router
    const downloadUrl = `/image/download?url=${encodeURIComponent(image.imageUrl)}&prompt=${encodeURIComponent(image.prompt)}`;
    navigate(downloadUrl);
  };



  // Delete image
  const handleDeleteImage = async (imageId) => {
    const confirmed = window.confirm('⚠️ Are you sure you want to delete this image?\n\nThis action cannot be undone.');
    if (!confirmed) return;

    try {
      console.log('🗑️ Deleting image:', imageId);
      const response = await fetch(`http://localhost:8000/api/admin/images/${imageId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        console.log('✅ Image deleted successfully');
        fetchImages(); // Refresh the list
        // Show success message (you can add a toast notification here)
      } else {
        console.error('❌ Failed to delete image:', response.status);
        alert('Failed to delete image. Please try again.');
      }
    } catch (error) {
      console.error('❌ Error deleting image:', error);
      alert('Error deleting image. Please check your connection.');
    }
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="min-h-screen bg-gray-900 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-gray-800 dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent animate-gradient-x">
                Admin Panel
              </h1>
              <p className="text-gray-300 dark:text-gray-300 mt-1">
                Manage all generated images
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="bg-blue-100 dark:bg-blue-900 px-4 py-2 rounded-lg">
                <div className="flex items-center space-x-2">
                  <ImageIcon className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  <span className="text-blue-800 dark:text-blue-200 font-semibold">
                    {stats.total || 0} Total Images
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
          <div className="bg-gray-800 dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                <Clock className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-300 dark:text-gray-300">Today</p>
                <p className="text-2xl font-semibold text-white dark:text-white">{stats.today || 0}</p>
              </div>
            </div>
          </div>

          <div className="bg-gray-800  dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-700 dark:border-gray-700">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                <Calendar className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-300 dark:text-gray-300">This Week</p>
                <p className="text-2xl font-semibold text-gray-300 dark:text-white">{stats.week || 0}</p>
              </div>
            </div>
          </div>

          <div className="bg-gray-800  dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-700 dark:border-gray-700">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                <BarChart3 className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-300 dark:text-gray-300">This Month</p>
                <p className="text-2xl font-semibold text-gray-300 dark:text-white">{stats.month || 0}</p>
              </div>
            </div>
          </div>

          <div className="bg-gray-800  dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-700 dark:border-gray-700">
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 dark:bg-orange-900 rounded-lg">
                <Grid3X3 className="h-6 w-6 text-orange-600 dark:text-orange-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-300 dark:text-gray-300">Total</p>
                <p className="text-2xl font-semibold text-gray-300 dark:text-white">{stats.total || 0}</p>
              </div>
            </div>
          </div>

          {/* Device/Location Stats */}
          <div className="bg-gray-800 dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-700 dark:border-gray-700">
            <div className="flex items-center">
              <div className="p-2 bg-indigo-100 dark:bg-indigo-900 rounded-lg">
                <Users className="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-300 dark:text-gray-300">Devices</p>
                <div className="flex items-center space-x-2 mt-1">
                  {(() => {
                    const deviceStats = images.reduce((acc, img) => {
                      const deviceInfo = parseDeviceInfo(img.userAgent);
                      acc[deviceInfo.device] = (acc[deviceInfo.device] || 0) + 1;
                      return acc;
                    }, {});

                    return Object.entries(deviceStats).slice(0, 2).map(([device, count]) => (
                      <span key={device} className="text-xs bg-indigo-100 dark:bg-indigo-800 text-indigo-800 dark:text-indigo-200 px-2 py-1 rounded">
                        {device}: {count}
                      </span>
                    ));
                  })()}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-gray-800 dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6 border border-gray-700 dark:border-gray-700">
          <div className="flex items-center space-x-4 mb-4">
            <Filter className="h-5 w-5 text-gray-500" />
            <h3 className="text-lg font-semibold text-gray-300 dark:text-white">Filters</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Aspect Ratio Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-300 dark:text-gray-300 mb-2">
                Aspect Ratio
              </label>
              <select
                value={filters.aspect_ratio}
                onChange={(e) => handleFilterChange('aspect_ratio', e.target.value)}
                className="w-full px-3 py-2 border border-gray-600 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="all">All Ratios</option>
                <option value="1:1">Square (1:1)</option>
                <option value="16:9">Landscape (16:9)</option>
                <option value="9:16">Portrait (9:16)</option>
                <option value="4:3">Standard (4:3)</option>
                <option value="3:4">Portrait (3:4)</option>
              </select>
            </div>

            {/* Category Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-300 dark:text-gray-300 mb-2">
                Category
              </label>
              <select
                value={filters.category}
                onChange={(e) => handleFilterChange('category', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="all">All Categories</option>
                <option value="t-shirt">T-Shirt</option>
                <option value="logo">Logo</option>
                <option value="people">People</option>
                <option value="nature">Nature</option>
                <option value="poster">Poster</option>
              </select>
            </div>

            {/* Time Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-300 dark:text-gray-300 mb-2">
                Time Period
              </label>
              <select
                value={filters.time_filter}
                onChange={(e) => handleFilterChange('time_filter', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="all">All Time</option>
                <option value="day">Last 24 Hours</option>
                <option value="week">Last Week</option>
                <option value="month">Last Month</option>
              </select>
            </div>
          </div>
        </div>

        {/* Images Grid */}
        <div className="bg-gray-800 dark:bg-gray-800 rounded-lg shadow-sm border border-gray-700 dark:border-gray-700">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-300 dark:text-white">
                Generated Images ({pagination.totalImages || 0})
              </h3>
              {pagination.totalPages > 1 && (
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handlePageChange(filters.page - 1)}
                    disabled={!pagination.hasPrev}
                    className="px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded disabled:opacity-50"
                  >
                    Previous
                  </button>
                  <span className="text-sm text-gray-300 dark:text-gray-400">
                    Page {pagination.currentPage} of {pagination.totalPages}
                  </span>
                  <button
                    onClick={() => handlePageChange(filters.page + 1)}
                    disabled={!pagination.hasNext}
                    className="px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded disabled:opacity-50"
                  >
                    Next
                  </button>
                </div>
              )}
            </div>
          </div>

          {loading ? (
            <div className="p-12 text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-600 dark:text-gray-300 mt-4">Loading images...</p>
            </div>
          ) : images.length === 0 ? (
            <div className="p-12 text-center">
              <ImageIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-300">No images found with current filters</p>
            </div>
          ) : (
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {images.map((image) => (
                  <div key={image._id} className="bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-sm hover:shadow-lg transition-all duration-200 border border-gray-200 dark:border-gray-700">
                    <div className="aspect-square relative group">
                      <img
                        src={image.imageUrl}
                        alt={image.prompt}
                        className="w-full h-full object-cover"
                        loading="lazy"
                      />
                      {/* Desktop Hover Overlay (hidden on mobile) */}
                      <div className="absolute inset-0 items-center justify-center hidden md:flex">
                        <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex space-x-3">
                          {/* View Button */}
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              setSelectedImage(image);
                            }}
                            className="p-3 bg-white/90 backdrop-blur-sm rounded-full hover:bg-white transition-colors shadow-xl transform hover:scale-110 border border-gray-200"
                            title="View Details"
                          >
                            <Eye className="h-5 w-5 text-gray-700" />
                          </button>

                          {/* Download Button */}
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDownloadClick(image);
                            }}
                            className="p-3 bg-white/90 backdrop-blur-sm rounded-full hover:bg-white transition-colors shadow-xl transform hover:scale-110 border border-gray-200"
                            title="Download Image"
                          >
                            <Download className="h-5 w-5 text-gray-700" />
                          </button>

                          {/* Delete Button */}
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteImage(image._id);
                            }}
                            className="p-3 bg-red-500/90 backdrop-blur-sm rounded-full hover:bg-red-500 transition-colors shadow-xl transform hover:scale-110 border border-red-400"
                            title="Delete Image"
                          >
                            <Trash2 className="h-5 w-5 text-white" />
                          </button>
                        </div>
                      </div>

                      {/* Mobile/Tablet Action Buttons (always visible on small screens) */}
                      <div className="absolute top-2 right-2 flex flex-col space-y-2 md:hidden">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedImage(image);
                          }}
                          className="p-2 bg-white/90 backdrop-blur-sm rounded-full shadow-lg border border-gray-200"
                          title="View Details"
                        >
                          <Eye className="h-4 w-4 text-gray-700" />
                        </button>

                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDownloadClick(image);
                          }}
                          className="p-2 bg-white/90 backdrop-blur-sm rounded-full shadow-lg border border-gray-200"
                          title="Download Image"
                        >
                          <Download className="h-4 w-4 text-gray-700" />
                        </button>

                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteImage(image._id);
                          }}
                          className="p-2 bg-red-500/90 backdrop-blur-sm rounded-full shadow-lg border border-red-400"
                          title="Delete Image"
                        >
                          <Trash2 className="h-4 w-4 text-white" />
                        </button>
                      </div>

                      {/* Click to view overlay */}
                      <div
                        className="absolute inset-0 cursor-pointer"
                        onClick={() => setSelectedImage(image)}
                      />
                    </div>
                    <div className="p-4">
                      <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2 mb-3">
                        {image.prompt}
                      </p>

                      {/* Device and Country Info */}
                      <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-2">
                        <div className="flex items-center space-x-2">
                          {/* Device Info */}
                          {(() => {
                            const deviceInfo = parseDeviceInfo(image.userAgent);
                            const DeviceIcon = deviceInfo.deviceIcon;
                            return (
                              <div className="flex items-center space-x-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded">
                                <DeviceIcon className="h-3 w-3" />
                                <span>{deviceInfo.device}</span>
                              </div>
                            );
                          })()}

                          {/* Country Info */}
                          <div className="flex items-center space-x-1 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded">
                            <Globe className="h-3 w-3" />
                            <span>
                              {(() => {
                                if (!image.ipAddress ||
                                    image.ipAddress === '::1' ||
                                    image.ipAddress === '127.0.0.1' ||
                                    image.ipAddress.includes('::ffff:127.0.0.1') ||
                                    image.ipAddress.includes('::ffff:192.168.') ||
                                    image.ipAddress.includes('::ffff:10.')) {
                                  return 'Local';
                                }
                                return countryData[image.ipAddress]?.country || 'External';
                              })()}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                        <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded">
                          {image.aspectRatio}
                        </span>
                        <span>{formatDate(image.createdAt)}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Image Modal */}
      {selectedImage && (
        <div
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"
          onClick={() => setSelectedImage(null)}
        >
          <div
            className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl max-h-full overflow-auto shadow-2xl"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Image Details</h3>
                <button
                  onClick={() => setSelectedImage(null)}
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 text-2xl font-bold w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  title="Close"
                >
                  ×
                </button>
              </div>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <img
                    src={selectedImage.imageUrl}
                    alt={selectedImage.prompt}
                    className="w-full rounded-lg"
                  />
                </div>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Prompt
                    </label>
                    <p className="text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                      {selectedImage.prompt}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Aspect Ratio
                    </label>
                    <p className="text-gray-900 dark:text-white">{selectedImage.aspectRatio}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Created At
                    </label>
                    <p className="text-gray-900 dark:text-white">{formatDate(selectedImage.createdAt)}</p>
                  </div>

                  {/* Device Information */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Device Information
                    </label>
                    <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                      {(() => {
                        const deviceInfo = parseDeviceInfo(selectedImage.userAgent);
                        const DeviceIcon = deviceInfo.deviceIcon;
                        return (
                          <div className="space-y-2">
                            <div className="flex items-center space-x-2">
                              <DeviceIcon className="h-4 w-4 text-gray-600 dark:text-gray-300" />
                              <span className="text-gray-900 dark:text-white">
                                <strong>Device:</strong> {deviceInfo.device}
                              </span>
                            </div>
                            <div className="text-gray-900 dark:text-white">
                              <strong>Browser:</strong> {deviceInfo.browser}
                            </div>
                            <div className="text-gray-900 dark:text-white">
                              <strong>OS:</strong> {deviceInfo.os}
                            </div>
                          </div>
                        );
                      })()}
                    </div>
                  </div>

                  {/* Location Information */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Location Information
                    </label>
                    <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Globe className="h-4 w-4 text-gray-600 dark:text-gray-300" />
                          <span className="text-gray-900 dark:text-white">
                            <strong>IP Address:</strong> {selectedImage.ipAddress || 'Unknown'}
                          </span>
                        </div>
                        <div className="text-gray-900 dark:text-white">
                          <strong>Country:</strong> {(() => {
                            if (!selectedImage.ipAddress ||
                                selectedImage.ipAddress === '::1' ||
                                selectedImage.ipAddress === '127.0.0.1' ||
                                selectedImage.ipAddress.includes('::ffff:127.0.0.1') ||
                                selectedImage.ipAddress.includes('::ffff:192.168.') ||
                                selectedImage.ipAddress.includes('::ffff:10.')) {
                              return 'Local Development';
                            }
                            return countryData[selectedImage.ipAddress]?.country || 'External Network';
                          })()}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex space-x-3">
                    <a
                      href={selectedImage.imageUrl}
                      download
                      className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-center transition-colors"
                    >
                      Download
                    </a>
                    <button
                      onClick={() => {
                        handleDeleteImage(selectedImage._id);
                        setSelectedImage(null);
                      }}
                      className="flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
                    >
                      Delete
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}


    </div>
  );
};

export default AdminPanel;
