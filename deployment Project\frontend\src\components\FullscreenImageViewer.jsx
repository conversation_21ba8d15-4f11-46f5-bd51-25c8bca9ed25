import React, { useEffect, useState } from 'react';
import { X, Download, ZoomIn, ZoomOut } from 'lucide-react';
import ShareButton from './ShareButton';

const FullscreenImageViewer = ({
  isOpen,
  imageUrl,
  imageAlt,
  onClose,
  onDownload,
  onShare
}) => {
  const [imageLoaded, setImageLoaded] = useState(false);

  useEffect(() => {
    if (isOpen) {
      console.log('🔍 FullscreenImageViewer opened');
      console.log('📸 Image URL received:', imageUrl);
      console.log('📝 Image Alt received:', imageAlt);
    }
  }, [isOpen, imageUrl, imageAlt]);

  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden'; // Prevent background scrolling
      setImageLoaded(false); // Reset image loaded state when opening
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  const handleImageLoad = () => {
    console.log('✅ Fullscreen image loaded successfully');
    setImageLoaded(true);
  };

  const handleImageError = (e) => {
    console.error('❌ Fullscreen image failed to load:', e.target.src);
    setImageLoaded(true); // Hide loading even on error
  };

  if (!isOpen) return null;

  // Check if imageUrl is valid
  if (!imageUrl) {
    console.warn('⚠️ No image URL provided to FullscreenImageViewer');
    return (
      <div className="fixed inset-0 bg-black bg-opacity-95 flex items-center justify-center z-50 p-4">
        <div className="text-center text-white">
          <div className="text-6xl mb-4">🖼️</div>
          <h3 className="text-xl font-semibold mb-2">Image Not Available</h3>
          <p className="text-gray-300 mb-4">The image could not be loaded.</p>
          <button
            onClick={onClose}
            className="px-6 py-2 bg-white text-black rounded-lg hover:bg-gray-100 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    );
  }

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleDownloadClick = (e) => {
    e.stopPropagation();
    onDownload();
  };

  const handleCloseClick = (e) => {
    e.stopPropagation();
    onClose();
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-95 flex items-center justify-center  "
      onClick={handleBackdropClick}
    >
      {/* Header Controls */}
      <div className="absolute top-0 right-4 flex gap-2 z-10">
        {onShare && (
          <div onClick={(e) => e.stopPropagation()}>
            <ShareButton
              imageUrl={imageUrl}
              prompt={imageAlt}
              onShare={onShare}
            />
          </div>
        )}
        {onDownload && (
          <button
            onClick={handleDownloadClick}
            className="p-3 bg-black bg-opacity-50 hover:bg-opacity-70 text-white rounded-lg
                     transition-all duration-200 backdrop-blur-sm"
            title="Download Image"
          >
            <Download className="w-5 h-5" />
          </button>
        )}
        <button
          onClick={handleCloseClick}
          className="p-3 bg-black bg-opacity-50 hover:bg-opacity-70 text-white rounded-lg
                   transition-all duration-200 backdrop-blur-sm"
          title="Close (ESC)"
        >
          <X className="w-5 h-5" />
        </button>
      </div>

      {/* Image Container - Fixed overflow issues */}
      <div className="relative w-full h-full flex items-center justify-center">
        <div className="relative max-w-[90vw] max-h-[90vh] flex items-center justify-center">
          <img
            src={imageUrl}
            alt={imageAlt}
            className="max-w-full max-h-full w-auto h-auto object-contain cursor-pointer rounded-lg shadow-2xl"
            style={{
              maxWidth: '90vw',
              maxHeight: '90vh',
            }}
            onClick={onClose}
            onLoad={handleImageLoad}
            onError={(e) => {
              e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzk5YTNhZiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vdCBhdmFpbGFibGU8L3RleHQ+PC9zdmc+';
              handleImageError(e);
            }}
          />
        </div>

      {/* Click hint - only show when image is loaded */}
      {imageLoaded && (
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2
                      bg-black bg-opacity-50 text-white px-4 py-2 rounded-lg text-sm
                      backdrop-blur-sm animate-fade-in z-10">
          Click image or press ESC to close
        </div>
      )}
      </div>

      {/* Loading indicator - only show when image is not loaded */}
      {!imageLoaded && (
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
          <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin opacity-50"></div>
        </div>
      )}
    </div>
  );
};

export default FullscreenImageViewer;
