import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import SEO from '../components/SEO';
import Logo from '../components/Logo';
import { Home, ArrowLeft, Sparkles, Zap, Heart, Star, Search, AlertTriangle } from 'lucide-react';

const NotFound = () => {
  const navigate = useNavigate();

  useEffect(() => {
    // Scroll to top when page loads
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  const handleGoHome = () => {
    navigate('/');
  };

  const handleGoBack = () => {
    navigate(-1);
  };

  const handleGoGenerate = () => {
    navigate('/generate');
  };

  return (
    <div className="relative min-h-screen flex items-center justify-center px-4">
      {/* SEO */}
      <SEO
        title="404 - Page Not Found | Gen Free AI"
        description="Oops! The page you're looking for doesn't exist. Return to Gen Free AI to create amazing AI-generated artwork."
        keywords="404, page not found, error, Gen Free AI"
      />

      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-900 to-gray-800 -z-10"></div>

      {/* Content */}
      <div className="max-w-4xl mx-auto text-center">
        {/* Animated Logo */}
        <div className="flex justify-center mb-8 animate-bounce-in" style={{ animationDelay: '0.2s' }}>
          <div className="relative">
            <Logo size="xl" />
            <div className="absolute -top-2 -right-2 animate-pulse">
              <AlertTriangle className="w-8 h-8 text-orange-500" />
            </div>
          </div>
        </div>

        {/* 404 Number */}
        <div className="mb-8 animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
          <h1 className="text-8xl md:text-9xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-orange-500 bg-clip-text text-transparent animate-gradient-x">
            404
          </h1>
        </div>

        {/* Main Message */}
        <div className="mb-8 animate-fade-in-up" style={{ animationDelay: '0.6s' }}>
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Oops! Page Not Found
          </h2>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto leading-relaxed">
            The page you're looking for seems to have vanished into the digital void.
            But don't worry – there's still plenty of AI magic to explore!
          </p>
        </div>

        {/* Search Suggestion */}
        <div className="mb-12 animate-fade-in-up" style={{ animationDelay: '0.8s' }}>
          <div className="flex items-center justify-center gap-3 text-gray-400 mb-6">
            <Search className="w-5 h-5" />
            <span className="text-sm">Maybe you were looking for:</span>
          </div>
          
          <div className="flex flex-wrap justify-center gap-3 text-sm">
            <button
              onClick={() => navigate('/generate')}
              className="px-4 py-2 bg-purple-900/30 text-purple-300 rounded-lg hover:bg-purple-900/50 transition-colors"
            >
              AI Image Generator
            </button>
            <button
              onClick={() => navigate('/blog')}
              className="px-4 py-2 bg-blue-900/30 text-blue-300 rounded-lg hover:bg-blue-900/50 transition-colors"
            >
              Blog & Tutorials
            </button>
            <button
              onClick={() => navigate('/history')}
              className="px-4 py-2 bg-green-900/30 text-green-300 rounded-lg hover:bg-green-900/50 transition-colors"
            >
              Image History
            </button>
            <button
              onClick={() => navigate('/about')}
              className="px-4 py-2 bg-orange-900/30 text-orange-300 rounded-lg hover:bg-orange-900/50 transition-colors"
            >
              About Us
            </button>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center animate-bounce-in" style={{ animationDelay: '1s' }}>
          <button
            onClick={handleGoHome}
            className="group relative inline-flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-purple-600 via-pink-600 to-orange-500 hover:from-purple-700 hover:via-pink-700 hover:to-orange-600 text-white text-lg font-bold rounded-xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
          >
            <Home className="w-5 h-5 group-hover:scale-110 transition-transform duration-300" />
            <span>Go Home</span>
          </button>

          <button
            onClick={handleGoBack}
            className="group inline-flex items-center gap-3 px-6 py-3 bg-gray-700 hover:bg-gray-600 text-gray-200 text-lg font-medium rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
          >
            <ArrowLeft className="w-5 h-5 group-hover:-translate-x-1 transition-transform duration-300" />
            <span>Go Back</span>
          </button>

          <button
            onClick={handleGoGenerate}
            className="group inline-flex items-center gap-3 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white text-lg font-medium rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
          >
            <Sparkles className="w-5 h-5 group-hover:rotate-12 transition-transform duration-300" />
            <span>Create AI Art</span>
          </button>
        </div>

        {/* Fun Stats */}
        <div className="mt-16 animate-fade-in-up" style={{ animationDelay: '1.2s' }}>
          <p className="text-sm text-gray-400 mb-6">
            While you're here, did you know?
          </p>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-2xl mx-auto">
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-900/30 rounded-xl flex items-center justify-center mx-auto mb-2">
                <Sparkles className="w-6 h-6 text-purple-400" />
              </div>
              <div className="text-2xl font-bold text-white">2K+</div>
              <div className="text-xs text-gray-400">Images Created</div>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-900/30 rounded-xl flex items-center justify-center mx-auto mb-2">
                <Zap className="w-6 h-6 text-blue-400" />
              </div>
              <div className="text-2xl font-bold text-white">⚡</div>
              <div className="text-xs text-gray-400">Lightning Fast</div>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-green-900/30 rounded-xl flex items-center justify-center mx-auto mb-2">
                <Heart className="w-6 h-6 text-green-400" />
              </div>
              <div className="text-2xl font-bold text-white">100%</div>
              <div className="text-xs text-gray-400">Free Forever</div>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-orange-900/30 rounded-xl flex items-center justify-center mx-auto mb-2">
                <Star className="w-6 h-6 text-orange-400" />
              </div>
              <div className="text-2xl font-bold text-white">HD</div>
              <div className="text-xs text-gray-400">Quality</div>
            </div>
          </div>
        </div>

        {/* Floating Elements */}
        <div className="absolute top-20 left-10 animate-float" style={{ animationDelay: '2s' }}>
          <Sparkles className="w-6 h-6 text-purple-400 opacity-60" />
        </div>
        <div className="absolute top-40 right-20 animate-float" style={{ animationDelay: '2.5s' }}>
          <Star className="w-5 h-5 text-pink-400 opacity-60" />
        </div>
        <div className="absolute bottom-40 left-20 animate-float" style={{ animationDelay: '3s' }}>
          <Heart className="w-4 h-4 text-red-400 opacity-60" />
        </div>
        <div className="absolute bottom-20 right-10 animate-float" style={{ animationDelay: '3.5s' }}>
          <Zap className="w-5 h-5 text-yellow-400 opacity-60" />
        </div>
      </div>
    </div>
  );
};

export default NotFound;
