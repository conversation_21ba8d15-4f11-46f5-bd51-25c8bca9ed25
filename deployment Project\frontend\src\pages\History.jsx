import React, { useState, useEffect } from 'react';
import { Search, Download, Trash2, Calendar, Image as ImageIcon, AlertCircle, Home, Clock, Filter, Grid, Heart, Share2, Eye, Star, Sparkles, Palette, Zap } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import {
  getImageHistory,
  deleteImageFromHistory,
  clearImageHistory,
  searchHistory,
  formatDate,
  getHistoryStats,
  isImageExpiring,
  isImageExpired,
  getTimeUntilExpiration,
  getExpiringImagesCount,
  getExpiringImages
} from '../utils/history';
import SEO, { pageSEO } from '../components/SEO';
// import FullscreenImageViewer from '../components/FullscreenImageViewer'; // Removed fullscreen functionality
import ShareButton from '../components/ShareButton';
import Breadcrumbs, { BreadcrumbStructuredData } from '../components/Breadcrumbs';

// Poppins and Roboto font styles
const fontStyles = `
  @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&family=Roboto:wght@300;400;500;700&display=swap');

  .poppins-font {
    font-family: 'Poppins', sans-serif;
  }

  .roboto-font {
    font-family: 'Roboto', sans-serif;
  }
`;


const History = () => {
  const [history, setHistory] = useState([]);
  const [filteredHistory, setFilteredHistory] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState({ totalImages: 0 });
  const [expiringCount, setExpiringCount] = useState(0);
  // Removed fullscreen state variables
  // Removed download modal states

  const navigate = useNavigate();

  useEffect(() => {
    loadHistory();
    // Scroll to top when page loads
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);



  // Refresh expiration status every minute
  useEffect(() => {
    const interval = setInterval(() => {
      const expiringImagesCount = getExpiringImagesCount();
      setExpiringCount(expiringImagesCount);
    }, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [history]);

  const loadHistory = () => {
    setIsLoading(true);
    try {
      const historyData = getImageHistory();
      const statsData = getHistoryStats();
      const expiringImagesCount = getExpiringImagesCount();
      setHistory(historyData);
      setFilteredHistory(historyData);
      setStats(statsData);
      setExpiringCount(expiringImagesCount);
    } catch (error) {
      // Error loading history
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = (imageId) => {
    if (window.confirm('Are you sure you want to delete this image from history?')) {
      const success = deleteImageFromHistory(imageId);
      if (success) {
        loadHistory();
      }
    }
  };

  const handleClearAll = () => {
    if (window.confirm('Are you sure you want to clear all history? This action cannot be undone.')) {
      const success = clearImageHistory();
      if (success) {
        loadHistory();
        setSearchTerm('');
      }
    }
  };

  const handleDownloadClick = (item) => {
    // Navigate to download page using React Router
    const downloadUrl = `/image/download?url=${encodeURIComponent(item.imageUrl)}&prompt=${encodeURIComponent(item.prompt)}`;
    navigate(downloadUrl);
  };



  // Removed fullscreen functionality

  const handleShare = () => {
    // You can add analytics or other tracking here
  };

  useEffect(() => {
    const filtered = searchHistory(searchTerm);
    setFilteredHistory(filtered);
  }, [searchTerm, history]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto"></div>
          <p className="mt-4 text-gray-300">Loading your history...</p>
        </div>
      </div>
    );
  }

  // Create breadcrumbs for history page
  const historyBreadcrumbs = [
    { label: 'Home', href: '/', icon: Home },
    { label: 'Generation History', href: null, icon: Clock }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 py-8 animate-fade-in relative overflow-hidden">
      {/* Add Poppins and Roboto font styles */}
      <style dangerouslySetInnerHTML={{ __html: fontStyles }} />

      {/* SEO */}
      <SEO
        title={pageSEO.history.title}
        description={pageSEO.history.description}
        keywords={pageSEO.history.keywords}
      />

      {/* Structured Data for Breadcrumbs */}
      <BreadcrumbStructuredData breadcrumbs={historyBreadcrumbs} />

      {/* Background Elements */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden -z-10">
        <div className="absolute top-20 left-10 w-32 h-32 bg-purple-800 rounded-full opacity-10 animate-float" style={{ animationDelay: '0s' }}></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-pink-800 rounded-full opacity-10 animate-float" style={{ animationDelay: '2s' }}></div>
        <div className="absolute bottom-40 left-20 w-20 h-20 bg-orange-800 rounded-full opacity-10 animate-float" style={{ animationDelay: '4s' }}></div>
        <div className="absolute bottom-20 right-10 w-28 h-28 bg-blue-800 rounded-full opacity-10 animate-float" style={{ animationDelay: '6s' }}></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Breadcrumbs */}
        <Breadcrumbs customBreadcrumbs={historyBreadcrumbs} />

        {/* Enhanced Header */}
        <div className="text-center mb-12">
          <div className="relative inline-block">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold animate-gradient-x mb-4 poppins-font">
              Your AI Image Gallery. Manage & Share  Your Creations

            </h1>
            <div className="absolute -top-2 -right-2 animate-bounce">
              <Sparkles className="w-8 h-8 text-yellow-500" />
            </div>
          </div>

          <p className="text-xl text-gray-300 mb-6 max-w-2xl mx-auto roboto-font">
           Browse all your AI‑generated images and artworks in one place
          </p>

          {/* Stats Cards */}
          <div className="flex flex-wrap justify-center gap-4 mb-8">
            <div className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-900 to-pink-900 rounded-full shadow-lg">
              <Palette className="w-5 h-5 text-purple-400" />
              <span className="font-semibold text-purple-300">{stats.totalImages} Creations</span>
            </div>
            {expiringCount > 0 && (
              <div className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-amber-900 to-orange-900 rounded-full shadow-lg animate-pulse">
                <AlertCircle className="w-5 h-5 text-amber-400" />
                <span className="font-semibold text-amber-300">{expiringCount} Expiring Soon</span>
              </div>
            )}
            <div className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-900 to-cyan-900 rounded-full shadow-lg">
              <Zap className="w-5 h-5 text-blue-400" />
              <span className="font-semibold text-blue-300">AI Powered</span>
            </div>
          </div>
        </div>

       

        {/* Enhanced Search and Actions */}
        <div className="bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800 rounded-2xl shadow-xl border border-gray-600 p-6 sm:p-8 mb-8">
          <div className="flex flex-col lg:flex-row gap-6 items-center justify-between">
            {/* Enhanced Search */}
            <div className="relative flex-1 max-w-2xl">
              <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                <Search className="w-6 h-6" />
              </div>
              <input
                type="text"
                placeholder="Search your AI creations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-12 pr-6 py-4 border-2 border-gray-600 rounded-xl
                         bg-gray-700 text-white text-lg
                         focus:ring-2 focus:ring-purple-500 focus:border-purple-500 focus:bg-gray-600
                         transition-all duration-200 shadow-inner"
              />
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-3">
              {/* View Toggle */}
              <div className="flex items-center bg-gray-700 rounded-xl p-1">
                <button className="p-2 rounded-lg bg-purple-600 text-white shadow-sm">
                  <Grid className="w-5 h-5" />
                </button>
              </div>

              {/* Clear All Button */}
              {history.length > 0 && (
                <button
                  onClick={handleClearAll}
                  className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800
                           text-white font-medium rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
                >
                  <Trash2 className="w-5 h-5" />
                  Clear All
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Enhanced Empty State */}
        {filteredHistory.length === 0 ? (
          <div className="text-center py-20">
            <div className="relative">
              <div className="w-32 h-32 sm:w-40 sm:h-40 mx-auto bg-gradient-to-br from-purple-900 via-pink-900 to-orange-900 rounded-3xl flex items-center justify-center mb-8 shadow-lg">
                {searchTerm ? (
                  <Search className="w-16 h-16 sm:w-20 sm:h-20 text-purple-400" />
                ) : (
                  <ImageIcon className="w-16 h-16 sm:w-20 sm:h-20 text-purple-400" />
                )}
              </div>
              <div className="absolute -top-2 -right-2 animate-bounce">
                <Sparkles className="w-8 h-8 text-yellow-500" />
              </div>
            </div>

            <h3 className="text-2xl sm:text-3xl font-bold text-white mb-4">
              {searchTerm ? 'No creations found' : 'Your gallery awaits'}
            </h3>
            <p className="text-lg text-gray-300 mb-8 max-w-md mx-auto">
              {searchTerm
                ? 'Try different search terms or clear your search to see all images'
                : 'Start creating amazing AI art to fill your personal gallery'
              }
            </p>

            {!searchTerm && (
              <button
                onClick={() => navigate('/generate')}
                className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-medium rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
              >
                <Palette className="w-5 h-5" />
                Create Your First Image
              </button>
            )}
          </div>
        ) : (
          /* Ideogram.ai Style Grid - 4 columns */
          <div className="columns-1 sm:columns-2 md:columns-3 lg:columns-4 gap-4 space-y-4">
            {filteredHistory.map((item, index) => (
              <div
                key={item.id}
                className="break-inside-avoid mb-4 group cursor-pointer"
                onClick={() => {
                  const params = new URLSearchParams({
                    url: encodeURIComponent(item.imageUrl),
                    prompt: encodeURIComponent(item.prompt),
                    resolution: item.aspectRatio || item.imageSize || '1024x1024'
                  });
                  navigate(`/image-detail?${params.toString()}`);
                }}
                style={{ animationDelay: `${index * 50}ms` }}
              >
                <div className="relative overflow-hidden rounded-xl bg-gray-800 shadow-lg hover:shadow-2xl transition-all duration-300 hover:scale-[1.02]">
                  <img
                    src={item.imageUrl}
                    alt={item.prompt}
                    className="w-full h-auto object-cover transition-all duration-300 group-hover:brightness-110"
                    onError={(e) => {
                      e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzk5YTNhZiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vdCBhdmFpbGFibGU8L3RleHQ+PC9zdmc+';
                    }}
                  />

                  {/* Hover Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                  {/* Action Buttons - Show on hover */}
                  <div className="absolute top-3 right-3 flex gap-2 opacity-0 group-hover:opacity-100 transition-all duration-300">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        window.open(item.imageUrl, '_blank');
                      }}
                      className="p-2 bg-gray-900/80 backdrop-blur-sm hover:bg-gray-800 rounded-lg transition-all duration-200 shadow-lg hover:scale-110"
                      title="View Full Size"
                    >
                      <Eye className="w-4 h-4 text-white" />
                    </button>

                    <ShareButton
                      imageUrl={item.imageUrl}
                      prompt={item.prompt}
                      onShare={handleShare}
                      className="p-2 bg-blue-600/90 backdrop-blur-sm hover:bg-blue-700 rounded-lg transition-all duration-200 shadow-lg hover:scale-110"
                    />

                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDownloadClick(item);
                      }}
                      className="p-2 bg-green-600/90 backdrop-blur-sm hover:bg-green-700 rounded-lg transition-all duration-200 shadow-lg hover:scale-110"
                      title="Download"
                    >
                      <Download className="w-4 h-4 text-white" />
                    </button>

                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDelete(item.id);
                      }}
                      className="p-2 bg-red-600/90 backdrop-blur-sm hover:bg-red-700 rounded-lg transition-all duration-200 shadow-lg hover:scale-110"
                      title="Delete"
                    >
                      <Trash2 className="w-4 h-4 text-white" />
                    </button>
                  </div>

                  {/* Bottom Info Overlay */}
                  <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <p className="text-white text-sm font-medium line-clamp-2 mb-2">
                      {item.prompt}
                    </p>
                    <div className="flex items-center justify-between text-xs text-gray-300">
                      <div className="flex items-center gap-1">
                        <Calendar className="w-3 h-3" />
                        <span>{formatDate(item.createdAt)}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <ImageIcon className="w-3 h-3" />
                        <span>{item.aspectRatio || item.imageSize || '1:1'}</span>
                      </div>
                    </div>
                  </div>

                  {/* Expiring Badge */}
                  {isImageExpiring(item.createdAt) && (
                    <div className="absolute top-3 left-3">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-amber-600/90 text-amber-100 backdrop-blur-sm">
                        <AlertCircle className="w-3 h-3 mr-1" />
                        Expiring
                      </span>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}


      </div>
    </div>
  );
};

export default History;
