import React from 'react';

// Facebook Icon
export const FacebookIcon = ({ className = "w-5 h-5" }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
    <path fillRule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clipRule="evenodd" />
  </svg>
);

// Twitter/X Icon
export const TwitterIcon = ({ className = "w-5 h-5" }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
  </svg>
);

// Instagram Icon
export const InstagramIcon = ({ className = "w-5 h-5" }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
    <path fillRule="evenodd" d="M12.017 0C8.396 0 7.989.013 7.041.072 6.094.131 5.42.333 4.844.63c-.611.324-1.13.756-1.649 1.275-.519.52-.951 1.038-1.275 1.649-.297.576-.499 1.25-.558 2.197C.013 7.989 0 8.396 0 12.017c0 3.624.013 4.09.072 5.016.059.947.261 1.621.558 2.197.324.611.756 1.13 1.275 1.649.52.519 1.038.951 1.649 1.275.576.297 1.25.499 2.197.558.927.059 1.334.072 5.016.072 3.624 0 4.09-.013 5.016-.072.947-.059 1.621-.261 2.197-.558.611-.324 1.13-.756 1.649-1.275.519-.52.951-1.038 1.275-1.649.297-.576.499-1.25.558-2.197.059-.927.072-1.334.072-5.016 0-3.624-.013-4.09-.072-5.016-.059-.947-.261-1.621-.558-2.197-.324-.611-.756-1.13-1.275-1.649-.52-.519-1.038-.951-1.649-1.275-.576-.297-1.25-.499-2.197-.558C16.107.013 15.7 0 12.017 0zM12.017 2.162c3.549 0 3.97.014 5.373.072.847.039 1.307.181 1.613.301.405.157.694.346.998.65.304.304.493.593.65.998.12.306.262.766.301 1.613.058 1.403.072 1.824.072 5.373 0 3.549-.014 3.97-.072 5.373-.039.847-.181 1.307-.301 1.613-.157.405-.346.694-.65.998-.304.304-.593.493-.998.65-.306.12-.766.262-1.613.301-1.403.058-1.824.072-5.373.072-3.549 0-3.97-.014-5.373-.072-.847-.039-1.307-.181-1.613-.301-.405-.157-.694-.346-.998-.65-.304-.304-.493-.593-.65-.998-.12-.306-.262-.766-.301-1.613-.058-1.403-.072-1.824-.072-5.373 0-3.549.014-3.97.072-5.373.039-.847.181-1.307.301-1.613.157-.405.346-.694.65-.998.304-.304.593-.493.998-.65.306-.12.766-.262 1.613-.301 1.403-.058 1.824-.072 5.373-.072z" clipRule="evenodd" />
    <path fillRule="evenodd" d="M12.017 5.838a6.179 6.179 0 1 0 0 12.358 6.179 6.179 0 0 0 0-12.358zM12.017 16a4 4 0 1 1 0-8 4 4 0 0 1 0 8z" clipRule="evenodd" />
    <circle cx="18.406" cy="5.594" r="1.44" />
  </svg>
);

// LinkedIn Icon
export const LinkedinIcon = ({ className = "w-5 h-5" }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
    <path fillRule="evenodd" d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" clipRule="evenodd" />
  </svg>
);

// TikTok Icon
export const TikTokIcon = ({ className = "w-5 h-5" }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
    <path d="M19.59 6.69a4.83 4.83 0 0 1-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 0 1-5.2 1.74 2.89 2.89 0 0 1 2.31-4.64 2.93 2.93 0 0 1 .88.13V9.4a6.84 6.84 0 0 0-1-.05A6.33 6.33 0 0 0 5 20.1a6.34 6.34 0 0 0 10.86-4.43v-7a8.16 8.16 0 0 0 4.77 1.52v-3.4a4.85 4.85 0 0 1-1-.1z" />
  </svg>
);

// YouTube Icon
export const YouTubeIcon = ({ className = "w-5 h-5" }) => (

  <svg className={className} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path d="M282 256.2l-95.2-54.1V310.3L282 256.2zM384 32H64C28.7 32 0 60.7 0 96V416c0 35.3 28.7 64 64 64H384c35.3 0 64-28.7 64-64V96c0-35.3-28.7-64-64-64zm14.4 136.1c7.6 28.6 7.6 88.2 7.6 88.2s0 59.6-7.6 88.1c-4.2 15.8-16.5 27.7-32.2 31.9C337.9 384 224 384 224 384s-113.9 0-142.2-7.6c-15.7-4.2-28-16.1-32.2-31.9C42 315.9 42 256.3 42 256.3s0-59.7 7.6-88.2c4.2-15.8 16.5-28.2 32.2-32.4C110.1 128 224 128 224 128s113.9 0 142.2 7.7c15.7 4.2 28 16.6 32.2 32.4z"/></svg>
);

export default {
  FacebookIcon,
  TwitterIcon,
  InstagramIcon,
  LinkedinIcon,
  TikTokIcon,
  YouTubeIcon
};
